# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""Dummy image generation for encoder benchmarking."""

import logging
from typing import Any, Dict, List, Tuple, Union
import torch
import numpy as np
from PIL import Image

from .config import EncoderBenchmarkConfig

logger = logging.getLogger(__name__)


class DummyImageGenerator:
    """Generates dummy images for benchmarking."""
    
    def __init__(self, config: EncoderBenchmarkConfig):
        self.config = config
        self._image_cache: Dict[Tuple[int, int, int], List[Image.Image]] = {}
    
    def generate_batch(self, 
                      batch_size: int, 
                      image_size: Tuple[int, int],
                      processor: Any) -> Dict[str, torch.Tensor]:
        """
        Generate a batch of dummy images and process them.
        
        Args:
            batch_size: Number of images in the batch
            image_size: (width, height) of images
            processor: HuggingFace processor for the model
            
        Returns:
            Processed tensor inputs ready for encoder
        """
        logger.debug(f"Generating batch of {batch_size} images with size {image_size}")
        
        # Generate dummy images
        images = self._generate_dummy_images(batch_size, image_size)
        
        # Process images using HF processor
        processed_inputs = self._process_images(images, processor)
        
        # Move to correct device and dtype
        processed_inputs = self._prepare_tensors(processed_inputs)
        
        return processed_inputs
    
    def _generate_dummy_images(self, batch_size: int, image_size: Tuple[int, int]) -> List[Image.Image]:
        """Generate dummy PIL images."""
        width, height = image_size
        cache_key = (batch_size, width, height)
        
        # Check cache first
        if cache_key in self._image_cache:
            return self._image_cache[cache_key]
        
        images = []
        for i in range(batch_size):
            # Generate random RGB image
            # Use different patterns to make images slightly different
            if i % 4 == 0:
                # Solid color
                color = (i * 50 % 256, (i * 100) % 256, (i * 150) % 256)
                image_array = np.full((height, width, 3), color, dtype=np.uint8)
            elif i % 4 == 1:
                # Gradient
                image_array = np.zeros((height, width, 3), dtype=np.uint8)
                for y in range(height):
                    image_array[y, :, 0] = (y * 255) // height
                    image_array[y, :, 1] = ((height - y) * 255) // height
                    image_array[y, :, 2] = 128
            elif i % 4 == 2:
                # Checkerboard pattern
                image_array = np.zeros((height, width, 3), dtype=np.uint8)
                checker_size = max(8, min(width, height) // 16)
                for y in range(height):
                    for x in range(width):
                        if (x // checker_size + y // checker_size) % 2:
                            image_array[y, x] = [255, 255, 255]
                        else:
                            image_array[y, x] = [0, 0, 0]
            else:
                # Random noise
                np.random.seed(i)  # Deterministic for reproducibility
                image_array = np.random.randint(0, 256, (height, width, 3), dtype=np.uint8)
            
            # Convert to PIL Image
            image = Image.fromarray(image_array, mode='RGB')
            images.append(image)
        
        # Cache the result
        self._image_cache[cache_key] = images
        
        return images
    
    def _process_images(self, images: List[Image.Image], processor: Any) -> Dict[str, torch.Tensor]:
        """Process images using HuggingFace processor."""
        try:
            # Most vision processors expect a list of images
            processed = processor(images=images, return_tensors="pt")
            
            # Handle different processor output formats
            if isinstance(processed, dict):
                return processed
            else:
                # Some processors might return tensors directly
                return {"pixel_values": processed}
                
        except Exception as e:
            logger.error(f"Failed to process images with processor: {e}")
            # Fallback: create dummy tensor with expected shape
            return self._create_fallback_tensors(len(images), processor)
    
    def _create_fallback_tensors(self, batch_size: int, processor: Any) -> Dict[str, torch.Tensor]:
        """Create fallback tensors when processor fails."""
        logger.warning("Using fallback tensor generation")
        
        # Try to infer expected input shape from processor
        try:
            if hasattr(processor, 'image_processor'):
                image_processor = processor.image_processor
            elif hasattr(processor, 'feature_extractor'):
                image_processor = processor.feature_extractor
            else:
                image_processor = processor
            
            # Get expected image size
            if hasattr(image_processor, 'size'):
                if isinstance(image_processor.size, dict):
                    height = image_processor.size.get('height', 224)
                    width = image_processor.size.get('width', 224)
                elif isinstance(image_processor.size, (list, tuple)):
                    height, width = image_processor.size
                else:
                    height = width = image_processor.size
            else:
                height = width = 224  # Default
            
            # Create dummy tensor [batch_size, channels, height, width]
            pixel_values = torch.randn(batch_size, 3, height, width)
            
            return {"pixel_values": pixel_values}
            
        except Exception as e:
            logger.error(f"Failed to create fallback tensors: {e}")
            # Ultimate fallback
            return {"pixel_values": torch.randn(batch_size, 3, 224, 224)}
    
    def _prepare_tensors(self, inputs: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Move tensors to correct device and dtype."""
        prepared_inputs = {}
        
        for key, tensor in inputs.items():
            if isinstance(tensor, torch.Tensor):
                # Move to device and convert dtype
                tensor = tensor.to(device=self.config.device)
                
                # Only convert floating point tensors to the target dtype
                if tensor.dtype.is_floating_point:
                    tensor = tensor.to(dtype=self.config.torch_dtype)
                
                prepared_inputs[key] = tensor
            else:
                # Keep non-tensor values as-is
                prepared_inputs[key] = tensor
        
        return prepared_inputs
    
    def get_expected_input_shape(self, processor: Any, batch_size: int) -> Tuple[int, ...]:
        """Get expected input tensor shape for the processor."""
        try:
            # Generate a single dummy image to determine shape
            dummy_image = Image.new('RGB', (224, 224), color='red')
            processed = processor(images=[dummy_image], return_tensors="pt")
            
            if isinstance(processed, dict) and "pixel_values" in processed:
                single_shape = processed["pixel_values"].shape[1:]  # Remove batch dimension
                return (batch_size,) + single_shape
            else:
                return (batch_size, 3, 224, 224)  # Default shape
                
        except Exception as e:
            logger.warning(f"Could not determine input shape: {e}")
            return (batch_size, 3, 224, 224)  # Default shape
    
    def clear_cache(self) -> None:
        """Clear the image cache."""
        self._image_cache.clear()
    
    def get_cache_info(self) -> Dict[str, int]:
        """Get information about the current cache."""
        total_images = sum(len(images) for images in self._image_cache.values())
        return {
            "cached_configurations": len(self._image_cache),
            "total_cached_images": total_images
        }
