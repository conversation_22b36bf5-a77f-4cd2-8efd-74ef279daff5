# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""Encoder module loader for benchmarking."""

import logging
from typing import Any, Dict, Optional, Tuple, Union
import torch
import torch.nn as nn
from transformers import AutoConfig, AutoProcessor

from vllm.config import VllmConfig, ModelConfig
from vllm.model_executor.models.clip import CLIPVisionModel, CLIPVisionConfig
from vllm.model_executor.models.siglip import SiglipVisionModel, SiglipVisionConfig
from vllm.model_executor.models.vision import get_vision_encoder_info

from .config import EncoderBenchmarkConfig

logger = logging.getLogger(__name__)


class EncoderLoader:
    """Loads and initializes encoder modules with dummy weights."""
    
    def __init__(self, config: EncoderBenchmarkConfig):
        self.config = config
        self._encoder_cache: Dict[str, Tu<PERSON>[nn.Mo<PERSON><PERSON>, <PERSON>]] = {}
    
    def load_encoder(self) -> <PERSON><PERSON>[nn.Modu<PERSON>, Any]:
        """
        Load encoder module and processor.
        
        Returns:
            Tuple of (encoder_model, hf_processor)
        """
        cache_key = f"{self.config.model_name}_{self.config.encoder_type}_{self.config.dtype}_{self.config.device}"
        
        if cache_key in self._encoder_cache:
            logger.info(f"Using cached encoder for {self.config.model_name}")
            return self._encoder_cache[cache_key]
        
        logger.info(f"Loading encoder for model: {self.config.model_name}")
        
        # Load HuggingFace config and processor
        hf_config = AutoConfig.from_pretrained(
            self.config.model_name,
            trust_remote_code=self.config.trust_remote_code
        )
        
        hf_processor = AutoProcessor.from_pretrained(
            self.config.model_name,
            trust_remote_code=self.config.trust_remote_code
        )
        
        # Determine encoder type if not specified
        encoder_type = self.config.encoder_type or self._detect_encoder_type(hf_config)
        
        # Create encoder model
        encoder_model = self._create_encoder_model(hf_config, encoder_type)
        
        # Apply optimizations
        encoder_model = self._optimize_model(encoder_model)
        
        # Cache the result
        result = (encoder_model, hf_processor)
        self._encoder_cache[cache_key] = result
        
        logger.info(f"Successfully loaded {encoder_type} encoder")
        return result
    
    def _detect_encoder_type(self, hf_config: Any) -> str:
        """Detect encoder type from HuggingFace config."""
        config_class_name = hf_config.__class__.__name__.lower()
        
        if "clip" in config_class_name:
            return "clip"
        elif "siglip" in config_class_name:
            return "siglip"
        elif "pixtral" in config_class_name:
            return "pixtral"
        elif hasattr(hf_config, 'vision_config'):
            # For multimodal models, check vision config
            vision_config_name = hf_config.vision_config.__class__.__name__.lower()
            if "clip" in vision_config_name:
                return "clip"
            elif "siglip" in vision_config_name:
                return "siglip"
        
        # Default fallback
        logger.warning(f"Could not detect encoder type from config {config_class_name}, defaulting to 'clip'")
        return "clip"
    
    def _create_encoder_model(self, hf_config: Any, encoder_type: str) -> nn.Module:
        """Create encoder model with dummy weights."""
        logger.info(f"Creating {encoder_type} encoder model")
        
        if encoder_type == "clip":
            return self._create_clip_encoder(hf_config)
        elif encoder_type == "siglip":
            return self._create_siglip_encoder(hf_config)
        else:
            raise ValueError(f"Unsupported encoder type: {encoder_type}")
    
    def _create_clip_encoder(self, hf_config: Any) -> CLIPVisionModel:
        """Create CLIP vision encoder."""
        # Extract vision config
        if hasattr(hf_config, 'vision_config'):
            vision_config = hf_config.vision_config
        else:
            # For standalone CLIP models
            vision_config = hf_config
        
        # Create vLLM CLIP vision model with dummy weights
        clip_model = CLIPVisionModel(
            config=vision_config,
            quant_config=None,  # No quantization for benchmarking
            prefix="vision_model"
        )
        
        # Initialize with dummy weights
        self._initialize_dummy_weights(clip_model)
        
        return clip_model
    
    def _create_siglip_encoder(self, hf_config: Any) -> SiglipVisionModel:
        """Create SigLIP vision encoder."""
        # Extract vision config
        if hasattr(hf_config, 'vision_config'):
            vision_config = hf_config.vision_config
        else:
            # For standalone SigLIP models
            vision_config = hf_config
        
        # Create vLLM SigLIP vision model with dummy weights
        siglip_model = SiglipVisionModel(
            config=vision_config,
            quant_config=None,  # No quantization for benchmarking
            prefix="vision_model"
        )
        
        # Initialize with dummy weights
        self._initialize_dummy_weights(siglip_model)
        
        return siglip_model
    
    def _initialize_dummy_weights(self, model: nn.Module) -> None:
        """Initialize model with dummy weights for benchmarking."""
        logger.info("Initializing model with dummy weights")
        
        def init_weights(module):
            if isinstance(module, nn.Linear):
                # Initialize linear layers with Xavier uniform
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Conv2d):
                # Initialize conv layers with Kaiming normal
                nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, (nn.LayerNorm, nn.GroupNorm, nn.BatchNorm2d)):
                # Initialize normalization layers
                if hasattr(module, 'weight') and module.weight is not None:
                    nn.init.ones_(module.weight)
                if hasattr(module, 'bias') and module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Embedding):
                # Initialize embedding layers
                nn.init.normal_(module.weight, mean=0.0, std=0.02)
        
        model.apply(init_weights)
    
    def _optimize_model(self, model: nn.Module) -> nn.Module:
        """Apply optimizations to the model."""
        # Move to device and set dtype
        model = model.to(device=self.config.device, dtype=self.config.torch_dtype)
        
        # Set to evaluation mode
        model.eval()
        
        # Apply torch.compile if requested
        if self.config.compile_model:
            logger.info("Compiling model with torch.compile")
            try:
                model = torch.compile(model, mode="reduce-overhead")
            except Exception as e:
                logger.warning(f"Failed to compile model: {e}")
        
        return model
    
    def get_encoder_info(self, hf_config: Any) -> Dict[str, Any]:
        """Get encoder information for reporting."""
        try:
            if hasattr(hf_config, 'vision_config'):
                vision_config = hf_config.vision_config
            else:
                vision_config = hf_config
            
            info = {
                "hidden_size": getattr(vision_config, 'hidden_size', None),
                "image_size": getattr(vision_config, 'image_size', None),
                "patch_size": getattr(vision_config, 'patch_size', None),
                "num_hidden_layers": getattr(vision_config, 'num_hidden_layers', None),
                "num_attention_heads": getattr(vision_config, 'num_attention_heads', None),
            }
            
            return {k: v for k, v in info.items() if v is not None}
        except Exception as e:
            logger.warning(f"Failed to extract encoder info: {e}")
            return {}
    
    def clear_cache(self) -> None:
        """Clear the encoder cache."""
        self._encoder_cache.clear()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
