# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""Configuration classes for encoder benchmarking."""

from dataclasses import dataclass, field
from typing import List, Optional, Tuple, Union
import torch


@dataclass
class EncoderBenchmarkConfig:
    """Configuration for encoder benchmarking."""
    
    # Model configuration
    model_name: str
    """HuggingFace model name or local path."""
    
    encoder_type: Optional[str] = None
    """Encoder type: 'clip', 'siglip', 'pixtral', etc. Auto-detected if None."""
    
    trust_remote_code: bool = False
    """Whether to trust remote code when loading models."""
    
    # Benchmark parameters
    batch_sizes: List[int] = field(default_factory=lambda: [1, 4, 8, 16])
    """List of batch sizes to benchmark."""
    
    image_sizes: List[Tuple[int, int]] = field(
        default_factory=lambda: [(224, 224), (336, 336), (512, 512)]
    )
    """List of (width, height) image sizes to benchmark."""
    
    num_iterations: int = 100
    """Number of iterations per configuration."""
    
    num_warmup: int = 10
    """Number of warmup iterations."""
    
    # Performance options
    device: str = "auto"
    """Device to run on: 'cuda', 'cpu', or 'auto'."""
    
    dtype: str = "float16"
    """Data type: 'float16', 'float32', 'bfloat16'."""
    
    compile_model: bool = False
    """Whether to use torch.compile for optimization."""
    
    # Output options
    output_format: str = "console"
    """Output format: 'console', 'json', 'csv'."""
    
    output_file: Optional[str] = None
    """Output file path. If None, prints to console."""
    
    include_system_info: bool = True
    """Whether to include system information in results."""
    
    verbose: bool = False
    """Whether to print verbose output during benchmarking."""
    
    def __post_init__(self):
        """Validate and normalize configuration."""
        # Validate batch sizes
        if not self.batch_sizes or any(bs <= 0 for bs in self.batch_sizes):
            raise ValueError("Batch sizes must be positive integers")
        
        # Validate image sizes
        if not self.image_sizes:
            raise ValueError("At least one image size must be specified")
        
        for width, height in self.image_sizes:
            if width <= 0 or height <= 0:
                raise ValueError("Image dimensions must be positive")
        
        # Validate iterations
        if self.num_iterations <= 0:
            raise ValueError("Number of iterations must be positive")
        
        if self.num_warmup < 0:
            raise ValueError("Number of warmup iterations cannot be negative")
        
        # Normalize device
        if self.device == "auto":
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # Validate dtype
        valid_dtypes = {"float16", "float32", "bfloat16"}
        if self.dtype not in valid_dtypes:
            raise ValueError(f"dtype must be one of {valid_dtypes}")
        
        # Validate output format
        valid_formats = {"console", "json", "csv"}
        if self.output_format not in valid_formats:
            raise ValueError(f"output_format must be one of {valid_formats}")
    
    @property
    def torch_dtype(self) -> torch.dtype:
        """Convert dtype string to torch.dtype."""
        dtype_map = {
            "float16": torch.float16,
            "float32": torch.float32,
            "bfloat16": torch.bfloat16,
        }
        return dtype_map[self.dtype]
    
    def get_total_configurations(self) -> int:
        """Get total number of benchmark configurations."""
        return len(self.batch_sizes) * len(self.image_sizes)


@dataclass
class BenchmarkResult:
    """Results from a single benchmark configuration."""
    
    batch_size: int
    image_size: Tuple[int, int]
    
    # Latency statistics (in milliseconds)
    latency_mean: float
    latency_std: float
    latency_p50: float
    latency_p90: float
    latency_p99: float
    
    # Throughput (images per second)
    throughput: float
    
    # Memory usage (in MB)
    memory_peak: Optional[float] = None
    memory_allocated: Optional[float] = None
    
    # Additional metadata
    num_iterations: int = 0
    num_warmup: int = 0


@dataclass
class SystemInfo:
    """System information for benchmark context."""
    
    # Hardware info
    gpu_name: Optional[str] = None
    gpu_memory_gb: Optional[float] = None
    cpu_name: Optional[str] = None
    cpu_cores: Optional[int] = None
    total_memory_gb: Optional[float] = None
    
    # Software info
    python_version: Optional[str] = None
    pytorch_version: Optional[str] = None
    cuda_version: Optional[str] = None
    vllm_version: Optional[str] = None
    
    # Benchmark info
    timestamp: Optional[str] = None
    model_name: Optional[str] = None
    encoder_type: Optional[str] = None
    device: Optional[str] = None
    dtype: Optional[str] = None


@dataclass
class BenchmarkReport:
    """Complete benchmark report."""
    
    config: EncoderBenchmarkConfig
    system_info: SystemInfo
    results: List[BenchmarkResult]
    
    def get_best_configuration(self, metric: str = "throughput") -> Optional[BenchmarkResult]:
        """Get the configuration with the best performance for a given metric."""
        if not self.results:
            return None
        
        if metric == "throughput":
            return max(self.results, key=lambda r: r.throughput)
        elif metric == "latency":
            return min(self.results, key=lambda r: r.latency_mean)
        else:
            raise ValueError(f"Unknown metric: {metric}")
    
    def get_results_for_batch_size(self, batch_size: int) -> List[BenchmarkResult]:
        """Get all results for a specific batch size."""
        return [r for r in self.results if r.batch_size == batch_size]
    
    def get_results_for_image_size(self, image_size: Tuple[int, int]) -> List[BenchmarkResult]:
        """Get all results for a specific image size."""
        return [r for r in self.results if r.image_size == image_size]
