# Standalone Encoder Benchmark Design

## Overview
This document outlines the design for a standalone encoder benchmark tool that measures multimodal encoder performance independently from the full vLLM pipeline.

## Requirements (from GitHub Issue #25450)
1. Import encoder modules from model file and initialize with dummy weights & vLLM config
2. Convert dummy images to encoder inputs via model's HF processor  
3. Measure latency processing batches of X images with Y sizes (configurable X and Y)

## Architecture Design

### 1. Core Components

```
benchmarks/
├── encoder_benchmark.py          # Main benchmark script
├── encoder_benchmark/
│   ├── __init__.py
│   ├── config.py                 # Configuration classes
│   ├── loader.py                 # Encoder module loader
│   ├── data_generator.py         # Dummy image generation
│   ├── processor.py              # HF processor wrapper
│   ├── measurement.py            # Performance measurement
│   └── reporter.py               # Results reporting
```

### 2. Configuration System

```python
@dataclass
class EncoderBenchmarkConfig:
    # Model configuration
    model_name: str                    # HF model name or path
    encoder_type: str                  # "clip", "siglip", "pixtral", etc.
    trust_remote_code: bool = False
    
    # Benchmark parameters
    batch_sizes: List[int]             # [1, 4, 8, 16, 32]
    image_sizes: List[Tuple[int, int]] # [(224, 224), (336, 336), (512, 512)]
    num_iterations: int = 100          # Iterations per configuration
    num_warmup: int = 10               # Warmup iterations
    
    # Performance options
    device: str = "cuda"               # "cuda", "cpu", "auto"
    dtype: str = "float16"             # "float16", "float32", "bfloat16"
    compile_model: bool = False        # Use torch.compile
    
    # Output options
    output_format: str = "json"        # "json", "csv", "console"
    output_file: Optional[str] = None
    include_system_info: bool = True
```

### 3. Encoder Module Loader

```python
class EncoderLoader:
    """Loads and initializes encoder modules with dummy weights."""
    
    def load_encoder(self, config: EncoderBenchmarkConfig) -> Tuple[nn.Module, Any]:
        """
        Load encoder module and processor.
        
        Returns:
            Tuple of (encoder_model, hf_processor)
        """
        # 1. Load HF config and determine encoder type
        # 2. Initialize encoder with dummy weights using vLLM patterns
        # 3. Load corresponding HF processor
        # 4. Apply optimizations (dtype, device, compile)
```

### 4. Data Generation Pipeline

```python
class DummyImageGenerator:
    """Generates dummy images for benchmarking."""
    
    def generate_batch(self, 
                      batch_size: int, 
                      image_size: Tuple[int, int],
                      processor: Any) -> Dict[str, torch.Tensor]:
        """
        Generate a batch of dummy images and process them.
        
        Returns:
            Processed tensor inputs ready for encoder
        """
        # 1. Generate random RGB images
        # 2. Apply HF processor transformations
        # 3. Return batched tensors
```

### 5. Performance Measurement

```python
class PerformanceMeasurer:
    """Measures encoder forward pass performance."""
    
    def benchmark_configuration(self,
                              encoder: nn.Module,
                              inputs: Dict[str, torch.Tensor],
                              num_iterations: int,
                              num_warmup: int) -> BenchmarkResult:
        """
        Benchmark a specific configuration.
        
        Returns:
            Performance metrics (latency, throughput, memory)
        """
        # 1. Warmup iterations
        # 2. Measure forward pass latency
        # 3. Calculate statistics (mean, std, percentiles)
        # 4. Monitor memory usage
```

### 6. CLI Interface

```bash
# Basic usage
vllm bench encoder --model llava-hf/llava-1.5-7b-hf

# Advanced configuration
vllm bench encoder \
    --model llava-hf/llava-1.5-7b-hf \
    --batch-sizes 1,4,8,16 \
    --image-sizes 224x224,336x336,512x512 \
    --iterations 100 \
    --warmup 10 \
    --device cuda \
    --dtype float16 \
    --compile \
    --output-json results.json

# Specific encoder types
vllm bench encoder --model openai/clip-vit-base-patch32 --encoder-type clip
vllm bench encoder --model google/siglip-base-patch16-224 --encoder-type siglip
```

## Implementation Plan

### Phase 1: Core Infrastructure
1. Create benchmark configuration system
2. Implement encoder loader for CLIP/SigLIP
3. Add dummy image generation
4. Basic performance measurement

### Phase 2: Advanced Features  
1. Support for more encoder types (Pixtral, etc.)
2. Memory usage monitoring
3. Torch.compile optimization
4. Comprehensive result reporting

### Phase 3: Integration & Testing
1. CLI integration with vLLM bench system
2. Comprehensive test suite
3. Documentation and examples
4. CI/CD integration

## Expected Output Format

```json
{
  "benchmark_info": {
    "model_name": "llava-hf/llava-1.5-7b-hf",
    "encoder_type": "clip",
    "device": "cuda",
    "dtype": "float16",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "system_info": {
    "gpu": "NVIDIA A100-SXM4-40GB",
    "cuda_version": "12.1",
    "pytorch_version": "2.1.0"
  },
  "results": [
    {
      "batch_size": 8,
      "image_size": [336, 336],
      "latency_ms": {
        "mean": 12.5,
        "std": 0.8,
        "p50": 12.3,
        "p90": 13.2,
        "p99": 14.1
      },
      "throughput_imgs_per_sec": 640.0,
      "memory_mb": {
        "peak": 2048,
        "allocated": 1536
      }
    }
  ]
}
```

## Benefits

1. **Isolated Performance**: Measure encoder performance without LLM overhead
2. **Optimization Guidance**: Identify optimal batch sizes and image dimensions
3. **Hardware Comparison**: Compare encoder performance across different GPUs
4. **Development Tool**: Help developers optimize encoder implementations
5. **CI/CD Integration**: Automated performance regression detection
